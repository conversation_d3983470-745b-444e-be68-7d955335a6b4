/*
 * ESP32-S3 Color Matching Device - Working Version with WiFi
 * 
 * A working version that compiles successfully and provides core functionality
 * with proper WiFi connectivity to "Wifi 6" network.
 * 
 * Features:
 * - TCS3430 color sensor reading with I2C communication
 * - WiFi connectivity using ESP32 built-in WiFi
 * - Simple color matching with built-in color database
 * - LED status indicators
 * - Serial output for debugging and data display
 * - Basic error handling and sensor health checks
 * 
 * Hardware Configuration:
 * - I2C: SDA=8, SCL=9
 * - Status LED: GPIO 5
 * - Illumination LED: GPIO 4  
 * - Sensor Interrupt: GPIO 21
 * 
 * Network Configuration:
 * - SSID: "Wifi 6"
 * - Password: "Scrofani1985"
 * - Static IP: *************
 */

#include <Arduino.h>
#include <Wire.h>
#include <WiFi.h>
#include <DFRobot_TCS3430.h>
#include <ArduinoJson.h>
#include <math.h>
#include <WebServer.h>

// ------------------------------------------------------------------------------------
// Configuration Constants
// ------------------------------------------------------------------------------------
#define I2C_SDA_PIN          8
#define I2C_SCL_PIN          9
#define STATUS_LED_PIN       5
#define ILLUMINATION_LED_PIN 4
#define SENSOR_INTERRUPT_PIN 21
#define READING_INTERVAL_MS  500

// Network Configuration
const char* WIFI_SSID = "Wifi 6";
const char* WIFI_PASSWORD = "Scrofani1985";
const IPAddress STATIC_IP(192, 168, 0, 201);
const IPAddress GATEWAY(192, 168, 0, 1);
const IPAddress SUBNET(255, 255, 255, 0);

// ------------------------------------------------------------------------------------
// Global Variables
// ------------------------------------------------------------------------------------
DFRobot_TCS3430 colorSensor;
unsigned long lastReading = 0;
unsigned long lastStatusBlink = 0;
bool statusLedState = false;
bool illuminationState = false;
bool sensorInitialized = false;
bool wifiConnected = false;

// Color data structure
struct ColorData {
    uint16_t x, y, z;
    uint16_t ir1, ir2;
    uint8_t r, g, b;
    char hexColor[8];
    char matchedColor[32];
    float deltaE;
};

ColorData currentColor = {0};

// Simple color database (expandable later)
struct ReferenceColor {
    const char* name;
    uint8_t r, g, b;
};

const ReferenceColor colorDatabase[] = {
    {"White", 255, 255, 255},
    {"Black", 0, 0, 0},
    {"Red", 255, 0, 0},
    {"Green", 0, 255, 0},
    {"Blue", 0, 0, 255},
    {"Yellow", 255, 255, 0},
    {"Cyan", 0, 255, 255},
    {"Magenta", 255, 0, 255},
    {"Orange", 255, 165, 0},
    {"Purple", 128, 0, 128},
    {"Brown", 165, 42, 42},
    {"Gray", 128, 128, 128},
    {"Pink", 255, 192, 203},
    {"Lime", 0, 255, 0},
    {"Navy", 0, 0, 128}
};

const int COLOR_DATABASE_SIZE = sizeof(colorDatabase) / sizeof(colorDatabase[0]);

// ------------------------------------------------------------------------------------
// Web Server Variables
// ------------------------------------------------------------------------------------
WebServer server(80);
bool isScanning = false;
unsigned long lastWebUpdate = 0;

// ------------------------------------------------------------------------------------
// Embedded Web UI Content
// ------------------------------------------------------------------------------------
const char* htmlPage = R"rawliteral(
<!DOCTYPE html>
<html>
<head>
    <title>ESP32 Color Matcher</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { font-family: Arial; margin: 0; padding: 20px; background: #f0f0f0; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        h1 { text-align: center; color: #333; }
        .color-box { width: 100%; height: 80px; border: 2px solid #ddd; margin: 20px 0; text-align: center; line-height: 80px; color: white; font-weight: bold; }
        .data { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 5px; }
        .controls { text-align: center; margin: 20px 0; }
        button { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; }
        .btn-blue { background: #007bff; color: white; }
        .btn-green { background: #28a745; color: white; }
        .btn-red { background: #dc3545; color: white; }
    </style>
    <script>
        function refreshData() {
            location.reload();
        }
        setInterval(refreshData, 2000);
    </script>
</head>
<body>
    <div class="container">
        <h1>ESP32 Color Matcher</h1>
        <div class="color-box" style="background-color: rgb(%RGB_VALUES%); color: %TEXT_COLOR%;">
            %HEX_COLOR% - %MATCHED_COLOR%
        </div>
        <div class="data">
            <strong>RGB:</strong> R:%R_VAL%, G:%G_VAL%, B:%B_VAL%
        </div>
        <div class="data">
            <strong>Matched Color:</strong> %MATCHED_COLOR%
        </div>
        <div class="data">
            <strong>Distance:</strong> %DELTA_E%
        </div>
        <div class="data">
            <strong>Raw XYZ:</strong> X:%X_VAL%, Y:%Y_VAL%, Z:%Z_VAL%
        </div>
        <div class="data">
            <strong>IR Values:</strong> IR1:%IR1_VAL%, IR2:%IR2_VAL%
        </div>
        <div class="data">
            <strong>Status:</strong> %SCAN_STATUS%
        </div>
        <div class="controls">
            <form action="/start_scan" method="post" style="display:inline;">
                <button type="submit" class="btn-blue">Start Scan</button>
            </form>
            <form action="/stop_scan" method="post" style="display:inline;">
                <button type="submit" class="btn-red">Stop Scan</button>
            </form>
            <form action="/toggle_led" method="post" style="display:inline;">
                <button type="submit" class="btn-green">Toggle LED</button>
            </form>
        </div>
    </div>
</body>
</html>
)rawliteral";

// ------------------------------------------------------------------------------------
// Helper Functions
// ------------------------------------------------------------------------------------
void blinkStatusLED() {
    if (millis() - lastStatusBlink > 1000) {
        statusLedState = !statusLedState;
        digitalWrite(STATUS_LED_PIN, statusLedState ? HIGH : LOW);
        lastStatusBlink = millis();
    }
}

void setIlluminationLED(bool state) {
    illuminationState = state;
    digitalWrite(ILLUMINATION_LED_PIN, state ? HIGH : LOW);
}

// Simple color distance calculation (Euclidean distance in RGB space)
float calculateColorDistance(uint8_t r1, uint8_t g1, uint8_t b1, uint8_t r2, uint8_t g2, uint8_t b2) {
    float dr = r1 - r2;
    float dg = g1 - g2;
    float db = b1 - b2;
    return sqrt(dr*dr + dg*dg + db*db);
}

// Find closest color match in database
void findColorMatch(uint8_t r, uint8_t g, uint8_t b) {
    float minDistance = 999999.0;
    int bestMatch = 0;
    
    for (int i = 0; i < COLOR_DATABASE_SIZE; i++) {
        float distance = calculateColorDistance(r, g, b, 
                                              colorDatabase[i].r, 
                                              colorDatabase[i].g, 
                                              colorDatabase[i].b);
        if (distance < minDistance) {
            minDistance = distance;
            bestMatch = i;
        }
    }
    
    strcpy(currentColor.matchedColor, colorDatabase[bestMatch].name);
    currentColor.deltaE = minDistance;
}

// Convert RGB to hex string
void rgbToHex(uint8_t r, uint8_t g, uint8_t b, char* hexStr) {
    sprintf(hexStr, "#%02X%02X%02X", r, g, b);
}

// ------------------------------------------------------------------------------------
// Sensor Functions
// ------------------------------------------------------------------------------------
bool initializeSensor() {
    Serial.println("Initializing TCS3430 color sensor...");
    
    if (!colorSensor.begin()) {
        Serial.println("ERROR: Failed to initialize TCS3430 sensor!");
        return false;
    }
    
    // Configure sensor settings
    colorSensor.setALSGain(1);           // Set ALS gain
    colorSensor.setIntegrationTime(0x40); // Set integration time
    
    Serial.println("TCS3430 sensor initialized successfully");
    return true;
}

bool readColorSensor() {
    // Read raw sensor data
    currentColor.x = colorSensor.getXData();
    currentColor.y = colorSensor.getYData();
    currentColor.z = colorSensor.getZData();
    currentColor.ir1 = colorSensor.getIR1Data();
    currentColor.ir2 = colorSensor.getIR2Data();
    
    // Simple XYZ to RGB conversion (basic approximation)
    // This is a simplified conversion - can be improved later
    float xNorm = currentColor.x / 65535.0;
    float yNorm = currentColor.y / 65535.0;
    float zNorm = currentColor.z / 65535.0;
    
    // Basic RGB conversion (simplified)
    currentColor.r = constrain(xNorm * 255, 0, 255);
    currentColor.g = constrain(yNorm * 255, 0, 255);
    currentColor.b = constrain(zNorm * 255, 0, 255);
    
    // Generate hex color string
    rgbToHex(currentColor.r, currentColor.g, currentColor.b, currentColor.hexColor);
    
    // Find closest color match
    findColorMatch(currentColor.r, currentColor.g, currentColor.b);
    
    return true;
}

// ------------------------------------------------------------------------------------
// Web Server Functions
// ------------------------------------------------------------------------------------
void handleRoot() {
    String html = String(htmlPage);

    // Replace placeholders with actual data
    html.replace("%RGB_VALUES%", String(currentColor.r) + "," + String(currentColor.g) + "," + String(currentColor.b));
    html.replace("%TEXT_COLOR%", (currentColor.r + currentColor.g + currentColor.b > 384) ? "black" : "white");
    html.replace("%HEX_COLOR%", String(currentColor.hexColor));
    html.replace("%MATCHED_COLOR%", String(currentColor.matchedColor));
    html.replace("%R_VAL%", String(currentColor.r));
    html.replace("%G_VAL%", String(currentColor.g));
    html.replace("%B_VAL%", String(currentColor.b));
    html.replace("%DELTA_E%", String(currentColor.deltaE, 1));
    html.replace("%X_VAL%", String(currentColor.x));
    html.replace("%Y_VAL%", String(currentColor.y));
    html.replace("%Z_VAL%", String(currentColor.z));
    html.replace("%IR1_VAL%", String(currentColor.ir1));
    html.replace("%IR2_VAL%", String(currentColor.ir2));
    html.replace("%SCAN_STATUS%", isScanning ? "Scanning..." : "Connected");

    server.send(200, "text/html", html);
}

void handleFullData() {
    JsonDocument doc;
    doc["data_ready"] = true;
    doc["measured_r"] = currentColor.r;
    doc["measured_g"] = currentColor.g;
    doc["measured_b"] = currentColor.b;
    doc["matched_name"] = currentColor.matchedColor;
    doc["matched_r"] = 0; // Will be filled from database
    doc["matched_g"] = 0;
    doc["matched_b"] = 0;
    doc["delta_e"] = currentColor.deltaE;
    doc["confidence"] = currentColor.deltaE < 10 ? "High" : currentColor.deltaE < 30 ? "Medium" : "Low";
    doc["is_scanning"] = isScanning;
    doc["avg_x"] = (float)currentColor.x;
    doc["avg_y"] = (float)currentColor.y;
    doc["avg_z"] = (float)currentColor.z;
    doc["avg_l"] = 0.0; // LAB conversion would go here
    doc["avg_a"] = 0.0;
    doc["avg_b"] = 0.0;
    doc["avg_ir1"] = (float)currentColor.ir1;
    doc["avg_ir2"] = (float)currentColor.ir2;
    doc["led_state"] = illuminationState;

    String json_response;
    serializeJson(doc, json_response);
    server.send(200, "application/json", json_response);
}

void handleStartScan() {
    isScanning = true;
    server.send(200, "text/plain", "Scan started");
}

void handleStopScan() {
    isScanning = false;
    server.send(200, "text/plain", "Scan stopped");
}

void handleToggleLED() {
    illuminationState = !illuminationState;
    setIlluminationLED(illuminationState);
    server.send(200, "text/plain", illuminationState ? "LED ON" : "LED OFF");
}

void setupWebServer() {
    // Setup web server routes
    server.on("/", handleRoot);
    server.on("/fulldata", handleFullData);
    server.on("/start_scan", HTTP_POST, handleStartScan);
    server.on("/stop_scan", HTTP_POST, handleStopScan);
    server.on("/toggle_led", HTTP_POST, handleToggleLED);

    // Start server
    server.begin();
    Serial.println("Web server started");
    Serial.print("Open http://");
    Serial.print(WiFi.localIP());
    Serial.println(" in your browser");
}

// ------------------------------------------------------------------------------------
// WiFi Functions
// ------------------------------------------------------------------------------------
void initializeWiFi() {
    Serial.println("Connecting to WiFi...");
    
    WiFi.mode(WIFI_STA);
    WiFi.config(STATIC_IP, GATEWAY, SUBNET);
    WiFi.begin(WIFI_SSID, WIFI_PASSWORD);
    
    int attempts = 0;
    while (WiFi.status() != WL_CONNECTED && attempts < 20) {
        delay(500);
        Serial.print(".");
        attempts++;
    }
    
    if (WiFi.status() == WL_CONNECTED) {
        wifiConnected = true;
        Serial.println();
        Serial.print("WiFi connected! IP address: ");
        Serial.println(WiFi.localIP());
    } else {
        Serial.println();
        Serial.println("WiFi connection failed - continuing without network");
        wifiConnected = false;
    }
}

// ------------------------------------------------------------------------------------
// Main Functions
// ------------------------------------------------------------------------------------
void setup() {
    Serial.begin(115200);
    delay(2000);
    
    Serial.println("============================================================");
    Serial.println("ESP32-S3 Color Matching Device - Working Version");
    Serial.println("============================================================");
    
    // Initialize GPIO pins
    pinMode(STATUS_LED_PIN, OUTPUT);
    pinMode(ILLUMINATION_LED_PIN, OUTPUT);
    pinMode(SENSOR_INTERRUPT_PIN, INPUT_PULLUP);
    
    digitalWrite(STATUS_LED_PIN, LOW);
    digitalWrite(ILLUMINATION_LED_PIN, LOW);
    
    // Initialize I2C
    Wire.begin(I2C_SDA_PIN, I2C_SCL_PIN);
    Wire.setClock(400000); // 400kHz
    Serial.println("I2C initialized");
    
    // Initialize color sensor
    sensorInitialized = initializeSensor();
    if (!sensorInitialized) {
        Serial.println("CRITICAL: Sensor initialization failed!");
        Serial.println("Check I2C connections and sensor power");
    }
    
    // Initialize WiFi
    initializeWiFi();

    // Initialize Web Server (only if WiFi connected)
    if (wifiConnected) {
        setupWebServer();
    }

    // Turn on illumination LED
    setIlluminationLED(true);

    Serial.println("============================================================");
    Serial.println("System initialization complete!");
    Serial.println("Starting color sensor readings...");
    Serial.println("============================================================");
}

void loop() {
    // Blink status LED to show system is alive
    blinkStatusLED();

    // Read color sensor at specified interval
    if (sensorInitialized && millis() - lastReading >= READING_INTERVAL_MS) {
        lastReading = millis();

        if (readColorSensor()) {
            // Print color data to serial
            Serial.println("--- Color Reading ---");
            Serial.printf("Raw XYZ: X=%d, Y=%d, Z=%d\n", currentColor.x, currentColor.y, currentColor.z);
            Serial.printf("Raw IR: IR1=%d, IR2=%d\n", currentColor.ir1, currentColor.ir2);
            Serial.printf("RGB: R=%d, G=%d, B=%d\n", currentColor.r, currentColor.g, currentColor.b);
            Serial.printf("Hex: %s\n", currentColor.hexColor);
            Serial.printf("Closest Match: %s (Distance: %.1f)\n", currentColor.matchedColor, currentColor.deltaE);
            Serial.printf("WiFi Status: %s\n", wifiConnected ? "Connected" : "Disconnected");
            Serial.println("--------------------");
        } else {
            Serial.println("ERROR: Failed to read color sensor");
        }
    }

    // Handle web server requests
    if (wifiConnected) {
        server.handleClient();
    }

    // Small delay to prevent overwhelming the system
    delay(10);
}
